package main

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
)

type FanMonitor struct {
	*BaseMonitorItem
	fanIndex int
}

func NewFanMonitor(fanIndex int, fanName string) *FanMonitor {
	name := fmt.Sprintf("fan%d", fanIndex)
	label := fanName
	if label == "" {
		label = fmt.Sprintf("Fan %d", fanIndex)
	}

	return &FanMonitor{
		BaseMonitorItem: NewBaseMonitorItem(name, label, 0, 5000, "RPM", 0),
		fanIndex:        fanIndex,
	}
}

func (f *FanMonitor) Update() error {
	fans := GetAvailableFans()
	if f.fanIndex-1 < len(fans) && f.fanIndex > 0 {
		f.SetValue(fans[f.fanIndex-1].Speed)
		f.SetAvailable(true)
	} else {
		f.SetAvailable(false)
	}
	return nil
}

func GetAvailableFans() []FanInfo {
	if runtime.GOOS == "windows" {
		return getWindowsFanInfo()
	}
	return getLinuxFanInfo()
}

func getWindowsFanInfo() []FanInfo {
	// Use cached GPU info for GPU fans
	fans := []FanInfo{}

	if cachedGPUInfo != nil && len(cachedGPUInfo.Fans) > 0 {
		fans = append(fans, cachedGPUInfo.Fans...)
	}

	// Add system fans (placeholder for Windows implementation)
	// In a real implementation, this would use WMI or hardware monitoring libraries
	systemFans := []FanInfo{
		{Name: "CPU Fan", Speed: 1200, Index: 1},
		{Name: "Case Fan 1", Speed: 800, Index: 2},
		{Name: "Case Fan 2", Speed: 850, Index: 3},
	}

	fans = append(fans, systemFans...)
	return fans
}

func getLinuxFanInfo() []FanInfo {
	if runtime.GOOS != "linux" {
		return []FanInfo{}
	}

	fans := []FanInfo{}
	fanIndex := 1

	hwmonDirs := []string{"/sys/class/hwmon"}
	for _, hwmonDir := range hwmonDirs {
		if entries, err := ioutil.ReadDir(hwmonDir); err == nil {
			for _, entry := range entries {
				if entry.IsDir() {
					hwmonPath := filepath.Join(hwmonDir, entry.Name())

					// Read hwmon name to identify the device
					nameFile := filepath.Join(hwmonPath, "name")
					var deviceName string
					if nameData, err := ioutil.ReadFile(nameFile); err == nil {
						deviceName = strings.TrimSpace(string(nameData))
					}

					// Find all fan input files
					fanFiles, _ := filepath.Glob(filepath.Join(hwmonPath, "fan*_input"))

					for _, fanFile := range fanFiles {
						if data, err := ioutil.ReadFile(fanFile); err == nil {
							if speed, err := strconv.Atoi(strings.TrimSpace(string(data))); err == nil && speed > 0 {
								// Extract fan number from filename
								baseName := filepath.Base(fanFile)
								fanNumStr := strings.TrimSuffix(strings.TrimPrefix(baseName, "fan"), "_input")

								fanName := fmt.Sprintf("Fan %s", fanNumStr)

								// Try to get a more descriptive name from label file
								labelFile := strings.Replace(fanFile, "_input", "_label", 1)
								if labelData, err := ioutil.ReadFile(labelFile); err == nil {
									labelName := strings.TrimSpace(string(labelData))
									if labelName != "" {
										fanName = labelName
									}
								}

								// Add device context if available
								if deviceName != "" {
									fanName = fmt.Sprintf("%s (%s)", fanName, deviceName)
								}

								fans = append(fans, FanInfo{
									Name:  fanName,
									Speed: speed,
									Index: fanIndex,
								})
								fanIndex++
							}
						}
					}
				}
			}
		}
	}

	return fans
}
